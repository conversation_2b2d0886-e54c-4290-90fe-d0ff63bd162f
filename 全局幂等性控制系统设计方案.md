# 全局幂等性控制系统设计方案

## 1. 系统概述

### 1.1 设计目标
- 提供统一的全局幂等性控制机制
- 支持多种幂等性策略和场景
- 与现有系统无缝集成
- 提供高性能和高可用性保障

### 1.2 技术架构
- **基础框架**：Spring Boot + AOP
- **缓存存储**：Redis (Redisson)
- **配置管理**：Spring Boot Configuration
- **监控统计**：Micrometer + 自定义指标

### 1.3 现状分析
项目当前已有`@RepeatSubmit`注解实现基础防重复提交功能，但存在以下局限性：
- 只支持时间窗口策略（默认5秒）
- 基于请求参数+token生成key，可能存在hash冲突
- 只适用于表单提交场景，不适合复杂业务幂等性需求
- 缺乏业务级别的幂等性控制

## 2. 核心组件设计

### 2.1 注解体系

#### @Idempotent 主注解
```java
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface Idempotent {
    
    /**
     * 幂等性策略
     */
    IdempotentStrategy strategy() default IdempotentStrategy.TIME_WINDOW;
    
    /**
     * 幂等性级别
     */
    IdempotentLevel level() default IdempotentLevel.USER;
    
    /**
     * 幂等性key，支持SpEL表达式
     */
    String key() default "";
    
    /**
     * 过期时间
     */
    long expireTime() default 300;
    
    /**
     * 时间单位
     */
    TimeUnit timeUnit() default TimeUnit.SECONDS;
    
    /**
     * 时间窗口间隔（用于TIME_WINDOW策略）
     */
    long interval() default 5;
    
    /**
     * 锁等待时间（用于DISTRIBUTED_LOCK策略）
     */
    long lockWaitTime() default 3;
    
    /**
     * 锁持有时间（用于DISTRIBUTED_LOCK策略）
     */
    long lockLeaseTime() default 10;
    
    /**
     * 是否启用结果缓存
     */
    boolean enableResultCache() default false;
    
    /**
     * 错误消息，支持国际化
     */
    String message() default "{idempotent.duplicate.request}";
    
    /**
     * 是否在处理中时快速失败
     */
    boolean failFast() default true;
}
```

#### @IdempotentKey 标记注解
```java
@Target(ElementType.PARAMETER)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface IdempotentKey {
    /**
     * key的权重，用于组合key时的排序
     */
    int order() default 0;
}
```

### 2.2 策略枚举

```java
public enum IdempotentStrategy {
    /**
     * 时间窗口策略 - 在指定时间窗口内防重复
     */
    TIME_WINDOW,
    
    /**
     * 业务唯一标识策略 - 基于业务唯一标识防重复
     */
    BUSINESS_UNIQUE,
    
    /**
     * 分布式锁策略 - 使用分布式锁确保唯一执行
     */
    DISTRIBUTED_LOCK,
    
    /**
     * 结果缓存策略 - 缓存执行结果，重复请求直接返回
     */
    RESULT_CACHE
}

public enum IdempotentLevel {
    /**
     * 全局级别
     */
    GLOBAL,
    
    /**
     * 用户级别
     */
    USER,
    
    /**
     * 租户级别
     */
    TENANT,
    
    /**
     * 自定义级别
     */
    CUSTOM
}

public enum IdempotentStatus {
    /**
     * 处理中
     */
    PROCESSING,
    
    /**
     * 成功完成
     */
    SUCCESS,
    
    /**
     * 执行失败
     */
    FAILED,
    
    /**
     * 已过期
     */
    EXPIRED
}
```

### 2.3 数据模型

```java
@Data
public class IdempotentRecord {
    /**
     * 幂等性key
     */
    private String key;
    
    /**
     * 策略类型
     */
    private IdempotentStrategy strategy;
    
    /**
     * 幂等性级别
     */
    private IdempotentLevel level;
    
    /**
     * 请求ID
     */
    private String requestId;
    
    /**
     * 用户ID
     */
    private String userId;
    
    /**
     * 租户ID
     */
    private String tenantId;
    
    /**
     * 方法签名
     */
    private String methodSignature;
    
    /**
     * 请求参数hash
     */
    private String paramsHash;
    
    /**
     * 状态
     */
    private IdempotentStatus status;
    
    /**
     * 执行结果（序列化后的JSON）
     */
    private String result;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 过期时间
     */
    private LocalDateTime expireTime;
}
```

## 3. Redis存储设计

### 3.1 存储结构
- 幂等性记录：`idempotent:{strategy}:{level}:{key}` -> IdempotentRecord
- 结果缓存：`idempotent:result:{key}` -> 方法执行结果
- 分布式锁：`idempotent:lock:{key}` -> 锁信息

### 3.2 TTL策略
- **时间窗口策略**：根据配置的时间窗口设置TTL
- **业务唯一标识**：根据业务特性设置较长TTL
- **分布式锁**：短TTL，防止死锁
- **结果缓存**：可配置TTL，支持永久缓存

## 4. 处理器设计

### 4.1 处理器接口

```java
public interface IdempotentProcessor {
    
    /**
     * 支持的策略类型
     */
    IdempotentStrategy supportStrategy();
    
    /**
     * 执行幂等性检查
     */
    IdempotentResult checkIdempotent(IdempotentContext context);
    
    /**
     * 处理成功后的操作
     */
    void afterSuccess(IdempotentContext context, Object result);
    
    /**
     * 处理失败后的操作
     */
    void afterFailure(IdempotentContext context, Throwable throwable);
}
```

### 4.2 具体处理器实现

#### 时间窗口处理器
```java
@Component
public class TimeWindowProcessor implements IdempotentProcessor {
    
    @Override
    public IdempotentStrategy supportStrategy() {
        return IdempotentStrategy.TIME_WINDOW;
    }
    
    @Override
    public IdempotentResult checkIdempotent(IdempotentContext context) {
        String redisKey = buildRedisKey(context);
        
        // 使用Redis的SET NX EX命令实现原子性检查和设置
        boolean success = RedisUtils.setObjectIfAbsent(
            redisKey, 
            buildRecord(context), 
            Duration.ofMillis(context.getInterval())
        );
        
        if (success) {
            return IdempotentResult.success();
        } else {
            return IdempotentResult.duplicate("请求过于频繁，请稍后再试");
        }
    }
}
```

#### 业务唯一标识处理器
```java
@Component
public class BusinessUniqueProcessor implements IdempotentProcessor {
    
    @Override
    public IdempotentStrategy supportStrategy() {
        return IdempotentStrategy.BUSINESS_UNIQUE;
    }
    
    @Override
    public IdempotentResult checkIdempotent(IdempotentContext context) {
        String redisKey = buildRedisKey(context);
        IdempotentRecord existingRecord = RedisUtils.getCacheObject(redisKey);
        
        if (existingRecord == null) {
            // 首次请求，创建记录
            IdempotentRecord record = buildRecord(context);
            record.setStatus(IdempotentStatus.PROCESSING);
            RedisUtils.setCacheObject(redisKey, record, Duration.ofSeconds(context.getExpireTime()));
            return IdempotentResult.success();
        }
        
        // 根据状态决定处理方式
        switch (existingRecord.getStatus()) {
            case PROCESSING:
                if (context.isFailFast()) {
                    return IdempotentResult.processing("请求处理中，请稍后");
                } else {
                    // 等待处理完成
                    return waitForCompletion(redisKey, context);
                }
            case SUCCESS:
                if (context.isEnableResultCache() && existingRecord.getResult() != null) {
                    return IdempotentResult.cached(existingRecord.getResult());
                } else {
                    return IdempotentResult.duplicate("业务已处理，请勿重复操作");
                }
            case FAILED:
                // 失败的请求可以重试
                existingRecord.setStatus(IdempotentStatus.PROCESSING);
                RedisUtils.setCacheObject(redisKey, existingRecord, Duration.ofSeconds(context.getExpireTime()));
                return IdempotentResult.success();
            default:
                return IdempotentResult.duplicate("未知状态");
        }
    }
}
```

#### 分布式锁处理器
```java
@Component
public class DistributedLockProcessor implements IdempotentProcessor {

    @Override
    public IdempotentStrategy supportStrategy() {
        return IdempotentStrategy.DISTRIBUTED_LOCK;
    }

    @Override
    public IdempotentResult checkIdempotent(IdempotentContext context) {
        String lockKey = buildLockKey(context);

        RLock lock = RedisUtils.getLock(lockKey);
        try {
            boolean acquired = lock.tryLock(
                context.getLockWaitTime(),
                context.getLockLeaseTime(),
                TimeUnit.SECONDS
            );

            if (acquired) {
                // 获取锁成功，检查是否已处理
                String recordKey = buildRedisKey(context);
                IdempotentRecord record = RedisUtils.getCacheObject(recordKey);

                if (record != null && record.getStatus() == IdempotentStatus.SUCCESS) {
                    if (context.isEnableResultCache() && record.getResult() != null) {
                        return IdempotentResult.cached(record.getResult());
                    } else {
                        return IdempotentResult.duplicate("操作已完成");
                    }
                }

                // 创建处理记录
                record = buildRecord(context);
                record.setStatus(IdempotentStatus.PROCESSING);
                RedisUtils.setCacheObject(recordKey, record, Duration.ofSeconds(context.getExpireTime()));

                // 将锁信息保存到上下文，在方法执行完成后释放
                context.setLock(lock);
                return IdempotentResult.success();
            } else {
                return IdempotentResult.lockFailed("系统繁忙，请稍后重试");
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            return IdempotentResult.lockFailed("获取锁被中断");
        }
    }
}
```

## 5. AOP切面实现

```java
@Aspect
@Component
@Slf4j
public class IdempotentAspect {

    private final Map<IdempotentStrategy, IdempotentProcessor> processorMap;
    private final IdempotentKeyGenerator keyGenerator;
    private final IdempotentProperties properties;

    private static final ThreadLocal<IdempotentContext> CONTEXT_HOLDER = new ThreadLocal<>();

    @Around("@annotation(idempotent)")
    public Object around(ProceedingJoinPoint joinPoint, Idempotent idempotent) throws Throwable {
        IdempotentContext context = buildContext(joinPoint, idempotent);
        CONTEXT_HOLDER.set(context);

        try {
            // 选择处理器
            IdempotentProcessor processor = processorMap.get(idempotent.strategy());
            if (processor == null) {
                throw new IdempotentException("不支持的幂等性策略: " + idempotent.strategy());
            }

            // 执行幂等性检查
            IdempotentResult checkResult = processor.checkIdempotent(context);

            switch (checkResult.getType()) {
                case SUCCESS:
                    // 首次请求，执行业务方法
                    Object result = joinPoint.proceed();
                    processor.afterSuccess(context, result);
                    return result;

                case CACHED:
                    // 返回缓存结果
                    return deserializeResult(checkResult.getCachedResult(), context.getReturnType());

                case DUPLICATE:
                case PROCESSING:
                case LOCK_FAILED:
                    // 重复请求或处理中或锁获取失败
                    throw new DuplicateRequestException(checkResult.getMessage());

                default:
                    throw new IdempotentException("未知的检查结果类型: " + checkResult.getType());
            }

        } catch (Throwable e) {
            // 异常处理
            IdempotentProcessor processor = processorMap.get(idempotent.strategy());
            if (processor != null) {
                processor.afterFailure(context, e);
            }
            throw e;
        } finally {
            // 清理上下文
            CONTEXT_HOLDER.remove();
            // 释放锁（如果有）
            if (context.getLock() != null) {
                context.getLock().unlock();
            }
        }
    }

    private IdempotentContext buildContext(ProceedingJoinPoint joinPoint, Idempotent idempotent) {
        // 构建上下文信息
        IdempotentContext context = new IdempotentContext();

        // 设置基本信息
        context.setStrategy(idempotent.strategy());
        context.setLevel(idempotent.level());
        context.setExpireTime(idempotent.expireTime());
        context.setTimeUnit(idempotent.timeUnit());
        context.setInterval(idempotent.timeUnit().toMillis(idempotent.interval()));
        context.setLockWaitTime(idempotent.lockWaitTime());
        context.setLockLeaseTime(idempotent.lockLeaseTime());
        context.setEnableResultCache(idempotent.enableResultCache());
        context.setFailFast(idempotent.failFast());
        context.setMessage(idempotent.message());

        // 设置方法信息
        context.setMethod(joinPoint.getSignature().toLongString());
        context.setArgs(joinPoint.getArgs());
        context.setReturnType(((MethodSignature) joinPoint.getSignature()).getReturnType());

        // 生成幂等性key
        String key = keyGenerator.generateKey(joinPoint, idempotent);
        context.setKey(key);

        // 设置用户和租户信息
        try {
            LoginUser loginUser = LoginHelper.getLoginUser();
            context.setUserId(loginUser.getUserId().toString());
            context.setTenantId(loginUser.getTenantId());
        } catch (Exception e) {
            // 未登录或获取用户信息失败，使用默认值
            context.setUserId("anonymous");
            context.setTenantId("default");
        }

        // 生成请求ID
        context.setRequestId(UUID.randomUUID().toString());

        return context;
    }
}
```

## 6. Key生成器设计

```java
public interface IdempotentKeyGenerator {
    String generateKey(ProceedingJoinPoint joinPoint, Idempotent idempotent);
}

@Component
public class SpELKeyGenerator implements IdempotentKeyGenerator {

    private final ExpressionParser parser = new SpelExpressionParser();
    private final ParameterNameDiscoverer parameterNameDiscoverer = new DefaultParameterNameDiscoverer();

    @Override
    public String generateKey(ProceedingJoinPoint joinPoint, Idempotent idempotent) {
        String keyExpression = idempotent.key();

        if (StringUtils.isEmpty(keyExpression)) {
            // 默认key生成策略
            return generateDefaultKey(joinPoint, idempotent);
        }

        // 使用SpEL表达式生成key
        return generateSpELKey(joinPoint, keyExpression);
    }

    private String generateDefaultKey(ProceedingJoinPoint joinPoint, Idempotent idempotent) {
        StringBuilder keyBuilder = new StringBuilder();

        // 添加方法签名
        keyBuilder.append(joinPoint.getSignature().toLongString());

        // 添加参数hash
        Object[] args = joinPoint.getArgs();
        if (args != null && args.length > 0) {
            keyBuilder.append(":").append(Arrays.hashCode(args));
        }

        // 根据级别添加用户或租户信息
        if (idempotent.level() == IdempotentLevel.USER) {
            try {
                LoginUser loginUser = LoginHelper.getLoginUser();
                keyBuilder.append(":user:").append(loginUser.getUserId());
            } catch (Exception e) {
                keyBuilder.append(":user:anonymous");
            }
        } else if (idempotent.level() == IdempotentLevel.TENANT) {
            try {
                LoginUser loginUser = LoginHelper.getLoginUser();
                keyBuilder.append(":tenant:").append(loginUser.getTenantId());
            } catch (Exception e) {
                keyBuilder.append(":tenant:default");
            }
        }

        return DigestUtils.md5Hex(keyBuilder.toString());
    }

    private String generateSpELKey(ProceedingJoinPoint joinPoint, String keyExpression) {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        Object[] args = joinPoint.getArgs();

        // 创建SpEL上下文
        EvaluationContext context = new StandardEvaluationContext();

        // 添加方法参数到上下文
        String[] parameterNames = parameterNameDiscoverer.getParameterNames(method);
        if (parameterNames != null) {
            for (int i = 0; i < parameterNames.length; i++) {
                context.setVariable(parameterNames[i], args[i]);
            }
        }

        // 添加常用变量
        try {
            LoginUser loginUser = LoginHelper.getLoginUser();
            context.setVariable("userId", loginUser.getUserId());
            context.setVariable("tenantId", loginUser.getTenantId());
        } catch (Exception e) {
            context.setVariable("userId", "anonymous");
            context.setVariable("tenantId", "default");
        }

        // 解析表达式
        Expression expression = parser.parseExpression(keyExpression);
        Object result = expression.getValue(context);

        return result != null ? result.toString() : "";
    }
}
```

## 7. 配置和自动装配

### 7.1 配置属性

```java
@ConfigurationProperties(prefix = "hengjian.idempotent")
@Data
public class IdempotentProperties {

    /**
     * 是否启用幂等性控制
     */
    private boolean enabled = true;

    /**
     * 默认策略
     */
    private IdempotentStrategy defaultStrategy = IdempotentStrategy.TIME_WINDOW;

    /**
     * 默认级别
     */
    private IdempotentLevel defaultLevel = IdempotentLevel.USER;

    /**
     * 默认过期时间（秒）
     */
    private long defaultExpireTime = 300;

    /**
     * Redis key前缀
     */
    private String keyPrefix = "idempotent";

    /**
     * 是否启用结果缓存
     */
    private boolean enableResultCache = true;

    /**
     * 最大缓存大小
     */
    private int maxCacheSize = 10000;

    /**
     * 锁等待时间（秒）
     */
    private long lockWaitTime = 3;

    /**
     * 锁持有时间（秒）
     */
    private long lockLeaseTime = 10;

    /**
     * 各策略的具体配置
     */
    private Map<String, StrategyConfig> strategies = new HashMap<>();

    @Data
    public static class StrategyConfig {
        private long defaultWindow = 5;
        private long maxWindow = 300;
        private long defaultExpire = 86400; // 24小时
        private int maxSize = 5000;
    }
}
```

### 7.2 自动配置类

```java
@AutoConfiguration
@ConditionalOnProperty(prefix = "hengjian.idempotent", name = "enabled", havingValue = "true", matchIfMissing = true)
@EnableConfigurationProperties(IdempotentProperties.class)
public class IdempotentAutoConfiguration {

    @Bean
    @ConditionalOnMissingBean
    public IdempotentKeyGenerator idempotentKeyGenerator() {
        return new SpELKeyGenerator();
    }

    @Bean
    public TimeWindowProcessor timeWindowProcessor() {
        return new TimeWindowProcessor();
    }

    @Bean
    public BusinessUniqueProcessor businessUniqueProcessor() {
        return new BusinessUniqueProcessor();
    }

    @Bean
    public DistributedLockProcessor distributedLockProcessor() {
        return new DistributedLockProcessor();
    }

    @Bean
    public ResultCacheProcessor resultCacheProcessor() {
        return new ResultCacheProcessor();
    }

    @Bean
    public IdempotentAspect idempotentAspect(
            List<IdempotentProcessor> processors,
            IdempotentKeyGenerator keyGenerator,
            IdempotentProperties properties) {
        return new IdempotentAspect(processors, keyGenerator, properties);
    }

    @Bean
    public IdempotentManager idempotentManager() {
        return new IdempotentManager();
    }
}
```

## 8. 使用示例

### 8.1 订单创建防重复

```java
@RestController
@RequestMapping("/order")
public class OrderController {

    @PostMapping("/create")
    @Idempotent(
        strategy = IdempotentStrategy.BUSINESS_UNIQUE,
        level = IdempotentLevel.USER,
        key = "#orderCreateBo.orderNo",
        expireTime = 24,
        timeUnit = TimeUnit.HOURS,
        message = "订单已存在，请勿重复创建"
    )
    public R<OrderVo> createOrder(@RequestBody @IdempotentKey OrderCreateBo orderCreateBo) {
        // 业务逻辑
        return orderService.createOrder(orderCreateBo);
    }
}
```

### 8.2 支付处理防并发

```java
@PostMapping("/pay")
@Idempotent(
    strategy = IdempotentStrategy.DISTRIBUTED_LOCK,
    level = IdempotentLevel.GLOBAL,
    key = "#payBo.orderNo + ':' + #payBo.payMethod",
    lockWaitTime = 5,
    lockLeaseTime = 30,
    message = "支付处理中，请稍后"
)
public R<PayResultVo> processPayment(@RequestBody PayBo payBo) {
    return paymentService.processPayment(payBo);
}
```

### 8.3 查询结果缓存

```java
@GetMapping("/product/{id}")
@Idempotent(
    strategy = IdempotentStrategy.RESULT_CACHE,
    level = IdempotentLevel.TENANT,
    key = "#productId + ':' + #tenantId",
    expireTime = 1,
    timeUnit = TimeUnit.HOURS,
    enableResultCache = true
)
public R<ProductVo> getProductDetail(@PathVariable Long productId) {
    return productService.getProductDetail(productId);
}
```

### 8.4 批量操作防重复

```java
@PostMapping("/batch-update")
@Idempotent(
    strategy = IdempotentStrategy.TIME_WINDOW,
    level = IdempotentLevel.USER,
    key = "T(java.util.Arrays).toString(#productIds)",
    interval = 10,
    timeUnit = TimeUnit.SECONDS
)
public R<Void> batchUpdateProducts(@RequestBody Long[] productIds) {
    return productService.batchUpdateProducts(productIds);
}
```

## 9. 配置示例

```yaml
hengjian:
  idempotent:
    enabled: true
    default-strategy: TIME_WINDOW
    default-level: USER
    default-expire-time: 300
    key-prefix: "idempotent"
    enable-result-cache: true
    max-cache-size: 10000
    lock-wait-time: 3
    lock-lease-time: 10
    strategies:
      time-window:
        default-window: 5
        max-window: 300
      business-unique:
        default-expire: 86400  # 24小时
      result-cache:
        default-expire: 3600   # 1小时
        max-size: 5000
```

## 10. 异常处理和错误码

### 10.1 异常类型定义

```java
public class IdempotentException extends RuntimeException {
    private String errorCode;

    public IdempotentException(String message) {
        super(message);
    }

    public IdempotentException(String errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
    }
}

public class DuplicateRequestException extends IdempotentException {
    public DuplicateRequestException(String message) {
        super("IDEMPOTENT_001", message);
    }
}

public class IdempotentLockException extends IdempotentException {
    public IdempotentLockException(String message) {
        super("IDEMPOTENT_002", message);
    }
}
```

### 10.2 错误码设计

```java
public enum IdempotentErrorCode {
    DUPLICATE_REQUEST("IDEMPOTENT_001", "重复请求"),
    LOCK_ACQUIRE_FAILED("IDEMPOTENT_002", "获取锁失败"),
    PROCESSING_IN_PROGRESS("IDEMPOTENT_003", "请求处理中"),
    CONFIG_ERROR("IDEMPOTENT_004", "配置错误"),
    KEY_GENERATE_FAILED("IDEMPOTENT_005", "Key生成失败"),
    STRATEGY_NOT_FOUND("IDEMPOTENT_006", "策略未找到"),
    CACHE_ERROR("IDEMPOTENT_007", "缓存操作失败");

    private final String code;
    private final String message;

    IdempotentErrorCode(String code, String message) {
        this.code = code;
        this.message = message;
    }
}
```

### 10.3 全局异常处理器扩展

```java
@RestControllerAdvice
public class IdempotentExceptionHandler {

    @ExceptionHandler(DuplicateRequestException.class)
    public R<Void> handleDuplicateRequestException(DuplicateRequestException e) {
        log.warn("重复请求异常: {}", e.getMessage());
        return R.fail(e.getMessage(), e.getErrorCode());
    }

    @ExceptionHandler(IdempotentLockException.class)
    public R<Void> handleIdempotentLockException(IdempotentLockException e) {
        log.warn("幂等性锁异常: {}", e.getMessage());
        return R.fail(e.getMessage(), e.getErrorCode());
    }

    @ExceptionHandler(IdempotentException.class)
    public R<Void> handleIdempotentException(IdempotentException e) {
        log.error("幂等性处理异常: {}", e.getMessage(), e);
        return R.fail("系统繁忙，请稍后重试", e.getErrorCode());
    }
}
```

## 11. 监控和管理

### 11.1 管理接口

```java
@RestController
@RequestMapping("/system/idempotent")
public class IdempotentManageController {

    private final IdempotentManager idempotentManager;

    @GetMapping("/records")
    public R<List<IdempotentRecord>> getRecords(
            @RequestParam(required = false) String key,
            @RequestParam(required = false) IdempotentStrategy strategy) {
        return R.ok(idempotentManager.getRecords(key, strategy));
    }

    @DeleteMapping("/clear/{key}")
    public R<Void> clearKey(@PathVariable String key) {
        idempotentManager.clearKey(key);
        return R.ok();
    }

    @GetMapping("/statistics")
    public R<IdempotentStatistics> getStatistics() {
        return R.ok(idempotentManager.getStatistics());
    }

    @PostMapping("/clear-expired")
    public R<Integer> clearExpiredRecords() {
        int count = idempotentManager.clearExpiredRecords();
        return R.ok("清理完成", count);
    }
}
```

### 11.2 统计指标

```java
@Component
public class IdempotentMetrics {

    private final Counter duplicateRequestCounter;
    private final Counter successRequestCounter;
    private final Timer processingTimer;
    private final Gauge cacheHitRatio;

    public IdempotentMetrics(MeterRegistry meterRegistry) {
        this.duplicateRequestCounter = Counter.builder("idempotent.duplicate.requests")
            .description("Number of duplicate requests")
            .register(meterRegistry);

        this.successRequestCounter = Counter.builder("idempotent.success.requests")
            .description("Number of successful requests")
            .register(meterRegistry);

        this.processingTimer = Timer.builder("idempotent.processing.time")
            .description("Time spent processing idempotent requests")
            .register(meterRegistry);

        this.cacheHitRatio = Gauge.builder("idempotent.cache.hit.ratio")
            .description("Cache hit ratio for idempotent requests")
            .register(meterRegistry, this, IdempotentMetrics::calculateCacheHitRatio);
    }

    public void recordDuplicateRequest(String strategy) {
        duplicateRequestCounter.increment(Tags.of("strategy", strategy));
    }

    public void recordSuccessRequest(String strategy) {
        successRequestCounter.increment(Tags.of("strategy", strategy));
    }

    public Timer.Sample startTimer() {
        return Timer.start(processingTimer);
    }

    private double calculateCacheHitRatio(IdempotentMetrics metrics) {
        // 计算缓存命中率的逻辑
        return 0.0; // 实际实现需要统计缓存命中和未命中次数
    }
}
```

### 11.3 管理器实现

```java
@Component
public class IdempotentManager {

    private final RedissonClient redissonClient;
    private final IdempotentProperties properties;

    public List<IdempotentRecord> getRecords(String keyPattern, IdempotentStrategy strategy) {
        String pattern = properties.getKeyPrefix() + ":*";
        if (StringUtils.isNotEmpty(keyPattern)) {
            pattern = properties.getKeyPrefix() + ":*" + keyPattern + "*";
        }

        RKeys keys = redissonClient.getKeys();
        Iterable<String> foundKeys = keys.getKeysByPattern(pattern);

        List<IdempotentRecord> records = new ArrayList<>();
        for (String key : foundKeys) {
            RBucket<IdempotentRecord> bucket = redissonClient.getBucket(key);
            IdempotentRecord record = bucket.get();
            if (record != null && (strategy == null || record.getStrategy() == strategy)) {
                records.add(record);
            }
        }

        return records;
    }

    public void clearKey(String key) {
        String fullKey = properties.getKeyPrefix() + ":" + key;
        redissonClient.getBucket(fullKey).delete();
    }

    public IdempotentStatistics getStatistics() {
        // 实现统计信息收集逻辑
        return new IdempotentStatistics();
    }

    public int clearExpiredRecords() {
        // 清理过期记录的逻辑
        return 0;
    }
}
```

## 12. 模块结构设计

```
hengjian-common-idempotent-plus/
├── src/main/java/com/hengjian/common/idempotent/
│   ├── annotation/                    # 注解定义
│   │   ├── Idempotent.java
│   │   ├── IdempotentKey.java
│   │   └── EnableIdempotent.java
│   ├── enums/                        # 枚举定义
│   │   ├── IdempotentStrategy.java
│   │   ├── IdempotentLevel.java
│   │   └── IdempotentStatus.java
│   ├── model/                        # 数据模型
│   │   ├── IdempotentRecord.java
│   │   ├── IdempotentResult.java
│   │   ├── IdempotentContext.java
│   │   └── IdempotentStatistics.java
│   ├── processor/                    # 处理器
│   │   ├── IdempotentProcessor.java
│   │   ├── TimeWindowProcessor.java
│   │   ├── BusinessUniqueProcessor.java
│   │   ├── DistributedLockProcessor.java
│   │   └── ResultCacheProcessor.java
│   ├── generator/                    # Key生成器
│   │   ├── IdempotentKeyGenerator.java
│   │   ├── DefaultKeyGenerator.java
│   │   └── SpELKeyGenerator.java
│   ├── aspect/                       # AOP切面
│   │   └── IdempotentAspect.java
│   ├── config/                       # 配置类
│   │   ├── IdempotentAutoConfiguration.java
│   │   └── IdempotentProperties.java
│   ├── exception/                    # 异常定义
│   │   ├── IdempotentException.java
│   │   ├── DuplicateRequestException.java
│   │   └── IdempotentLockException.java
│   ├── manager/                      # 管理器
│   │   └── IdempotentManager.java
│   ├── metrics/                      # 监控指标
│   │   └── IdempotentMetrics.java
│   └── controller/                   # 管理接口
│       └── IdempotentManageController.java
├── src/main/resources/
│   ├── META-INF/
│   │   └── spring.factories
│   └── i18n/
│       └── idempotent-messages.properties
└── pom.xml
```

## 13. 实施计划

### 13.1 分阶段实施

#### 第一阶段：核心框架（1-2周）
- [ ] 创建基础模块结构
- [ ] 实现核心注解和枚举
- [ ] 实现数据模型和上下文
- [ ] 实现时间窗口处理器
- [ ] 实现基础AOP切面
- [ ] 实现SpEL Key生成器
- [ ] 编写单元测试

#### 第二阶段：业务策略（1-2周）
- [ ] 实现业务唯一标识处理器
- [ ] 实现分布式锁处理器
- [ ] 完善异常处理机制
- [ ] 实现配置属性和自动配置
- [ ] 编写集成测试

#### 第三阶段：高级功能（1-2周）
- [ ] 实现结果缓存处理器
- [ ] 实现管理接口和统计功能
- [ ] 实现监控指标收集
- [ ] 完善文档和示例
- [ ] 性能测试和优化

#### 第四阶段：部署和优化（1周）
- [ ] 生产环境部署
- [ ] 监控和告警配置
- [ ] 性能调优
- [ ] 用户培训和文档完善

### 13.2 迁移策略

#### 兼容性保证
- 保持现有`@RepeatSubmit`注解的完全兼容
- 新功能使用新的`@Idempotent`注解
- 提供迁移工具和指南

#### 渐进式迁移
1. **试点阶段**：选择1-2个非关键接口进行试点
2. **扩展阶段**：逐步迁移重要业务接口
3. **全面推广**：在验证稳定性后全面推广
4. **清理阶段**：逐步替换旧的实现

### 13.3 风险控制

#### 技术风险
- Redis性能影响：通过连接池优化和批量操作减少影响
- 内存占用：设置合理的TTL和缓存大小限制
- 并发性能：使用高效的分布式锁实现

#### 业务风险
- 误判重复请求：提供手动清理和重试机制
- 缓存一致性：设计合理的缓存更新策略
- 系统可用性：提供降级机制和监控告警

## 14. 性能考虑

### 14.1 性能优化策略
- **Redis连接池优化**：合理配置连接池参数
- **序列化优化**：使用高效的序列化方式
- **批量操作**：支持批量清理和查询
- **异步处理**：非关键路径使用异步处理
- **缓存预热**：系统启动时预热常用缓存

### 14.2 性能指标
- **响应时间**：幂等性检查耗时 < 10ms
- **吞吐量**：支持1000+ QPS的幂等性检查
- **内存占用**：单个记录 < 1KB
- **缓存命中率**：> 90%

## 15. 总结

本设计方案提供了一个完整、灵活、可扩展的全局幂等性控制系统，具有以下特点：

### 15.1 核心优势
- **多策略支持**：时间窗口、业务唯一标识、分布式锁、结果缓存
- **灵活配置**：支持SpEL表达式和多级别控制
- **高性能**：基于Redis的高效实现
- **易于使用**：注解驱动，简单易用
- **完善监控**：提供统计和管理功能

### 15.2 适用场景
- **订单系统**：防止重复下单
- **支付系统**：防止重复支付
- **库存系统**：防止超卖
- **数据同步**：防止重复处理
- **API接口**：防止重复调用

### 15.3 扩展性
- **处理器扩展**：支持自定义处理策略
- **Key生成扩展**：支持自定义Key生成逻辑
- **监控扩展**：支持自定义监控指标
- **存储扩展**：支持多种存储后端

该方案与您现有的技术栈完美集成，能够满足各种复杂的业务幂等性需求，为系统的稳定性和可靠性提供强有力的保障。
