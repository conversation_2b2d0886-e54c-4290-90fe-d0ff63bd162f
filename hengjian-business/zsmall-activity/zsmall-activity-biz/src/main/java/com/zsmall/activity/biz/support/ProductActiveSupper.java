package com.zsmall.activity.biz.support;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hengjian.common.core.domain.RStatusCodeBase;
import com.hengjian.common.core.exception.RStatusCodeException;
import com.hengjian.common.core.utils.StringUtils;
import com.hengjian.common.log.annotation.InMethodLog;
import com.hengjian.common.redis.utils.RedisUtils;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.hengjian.openapi.domain.SysInfEnum;
import com.hengjian.openapi.domain.vo.SysInfVo;
import com.hengjian.openapi.service.ISysInfService;
import com.zsmall.activity.entity.domain.bo.productActivity.ActivityDepositPayBo;
import com.zsmall.activity.entity.domain.dto.productActivity.*;
import com.zsmall.activity.entity.domain.dto.productActivity.erp.ErpProductLockInventoryReleaseRequest;
import com.zsmall.activity.entity.domain.dto.productActivity.erp.ErpProductLockInventoryReservedRequest;
import com.zsmall.activity.entity.iservice.IDistributorProductActivityService;
import com.zsmall.activity.entity.iservice.IDistributorProductActivityStockService;
import com.zsmall.activity.entity.iservice.ISupplierProductActivityService;
import com.zsmall.activity.entity.iservice.ISupplierProductActivityStockService;
import com.zsmall.common.constant.RedisConstants;
import com.zsmall.common.domain.dto.stock.AdjustStockDTO;
import com.zsmall.common.enums.BusinessCodeEnum;
import com.zsmall.common.enums.order.LogisticsTypeEnum;
import com.zsmall.common.enums.productActivity.ProductActivityStateEnum;
import com.zsmall.common.enums.statuscode.OrderStatusCodeEnum;
import com.zsmall.common.enums.statuscode.ZSMallStatusCodeEnum;
import com.zsmall.common.enums.transaction.TransactionStateEnum;
import com.zsmall.common.enums.transaction.TransactionSubTypeEnum;
import com.zsmall.common.enums.transaction.TransactionTypeEnum;
import com.zsmall.common.exception.StockException;
import com.zsmall.system.biz.service.TenantWalletService;
import com.zsmall.system.entity.domain.TenantWallet;
import com.zsmall.system.entity.domain.TransactionRecord;
import com.zsmall.system.entity.iservice.ITransactionRecordService;
import com.zsmall.system.entity.mapper.TenantWalletMapper;
import com.zsmall.system.entity.util.MallSystemCodeGenerator;
import com.zsmall.warehouse.entity.iservice.IWarehouseService;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 库存的锁定/释放 对于分销系统而言都是基于供应商活动
 * <AUTHOR>
 */
@Slf4j
@Service
public class ProductActiveSupper {
     @Resource
     private ISysInfService sysInfService;
     @Resource
     private ISupplierProductActivityService supplierProductActivityService;
     @Resource
     private IDistributorProductActivityService distributorProductActivityService;
     @Resource
     private IDistributorProductActivityStockService distributorProductActivityStockService;
     @Resource
     private ISupplierProductActivityStockService supplierProductActivityStockService;
     @Resource
     private IWarehouseService iWarehouseService;
     @Resource
     private TenantWalletService tenantWalletService;
     @Resource
     private MallSystemCodeGenerator mallSystemCodeGenerator;
     @Resource
     private TenantWalletMapper tenantWalletMapper;
     @Resource
     private ITransactionRecordService iTransactionRecordService;



    /**
     * ERP锁定库存(目前只有供应商审核一个入口，外部禁止调用)
     * @param reservedRequest 锁货请求
     * @param supplierActivityCode 供应商活动编码
     * @return Boolean
     */
    public Boolean erpProductLockInventoryReserved(String supplierActivityCode ,ErpProductLockInventoryReservedRequest reservedRequest){
        try {
            verificationReservedRequest(supplierActivityCode,reservedRequest);
            return erpProductLockRequest(SysInfEnum.ERP_PRODUCT_LOCK_INVENTORY_RESERVED, JSONUtil.toJsonStr(reservedRequest), true);
        }catch (Exception e){
            log.error("ERP库存锁定失败",e);
            return false;
        }
    }
    /**
     * 校验ERP锁定库存请求参数
     * @param supplierActivityCode 供应商活动编号
     * @param reservedRequest 释放库存请求
     */
    private void verificationReservedRequest(String supplierActivityCode ,ErpProductLockInventoryReservedRequest reservedRequest) {
        LambdaQueryWrapper<SupplierProductActivity> q = new LambdaQueryWrapper<>();
        q.eq(SupplierProductActivity::getSupplierActivityCode,supplierActivityCode);
        q.eq(SupplierProductActivity::getDelFlag,0);
        SupplierProductActivity one = supplierProductActivityService.getOne(q);
        if (ObjectUtil.isEmpty(one)){
            throw new RuntimeException(StrUtil.format("[ERP库存锁定],活动编号:{} 不存在",supplierActivityCode));
        }
        if (StrUtil.isEmpty(reservedRequest.getOrgWarehouseCode())){
            throw new RuntimeException("[ERP库存锁定], 仓库编码不能为空");
        }
        LambdaQueryWrapper<SupplierProductActivityStock> qq = new LambdaQueryWrapper<>();
        qq.eq(SupplierProductActivityStock::getSupplierActivityCode,supplierActivityCode);
        qq.eq(SupplierProductActivityStock::getWarehouseSystemCode,reservedRequest.getOrgWarehouseCode());
        qq.eq(SupplierProductActivityStock::getDelFlag,0);
        SupplierProductActivityStock stock = supplierProductActivityStockService.getOne(qq);
        if (ObjectUtil.isEmpty(stock)){
            throw new RuntimeException(StrUtil.format("[ERP库存锁定],活动编号:{} 仓库编码:{} 不存在",supplierActivityCode,reservedRequest.getOrgWarehouseCode()));
        }
        if (ObjectUtil.notEqual(reservedRequest.getSourceNo(),one.getSupplierTenantId())){
            throw new RuntimeException(StrUtil.format("[ERP库存锁定],活动编号:{} 仓库编码:{} 锁货标识:{} 不存在",supplierActivityCode,reservedRequest.getOrgWarehouseCode(),reservedRequest.getSourceNo()));
        }

        if (ObjectUtil.isEmpty(reservedRequest.getInventoryType())){
            throw new RuntimeException("[ERP库存锁定], 库存类型不能为空");
        }
        if (ObjectUtil.isEmpty(reservedRequest.getSrcLabel())){
            throw new RuntimeException("[ERP库存锁定], 来源类型不能为空");
        }
        if (StrUtil.isEmpty(reservedRequest.getSourceNo())){
            throw new RuntimeException("[ERP库存锁定], 锁货标识不能为空");
        }

        reservedRequest.getProductItemList().forEach(s->{
            if (ObjectUtil.isEmpty(s.getSku())){
                throw new RuntimeException("[ERP库存锁定], 产品编码不能为空");
            }
            if (ObjectUtil.notEqual(s.getSku(),one.getProductSku())){
                throw new RuntimeException(StrUtil.format("[ERP库存锁定], 产品编码:{} 不属于活动商品",s.getSku()));
            }
            if (ObjectUtil.isEmpty(s.getQuantity())){
                throw new RuntimeException("[ERP库存锁定], 锁货数量不能为空");
            }
            if (StringUtils.isEmpty(s.getTrackCode())){
                throw new RuntimeException("[ERP库存锁定], 预留追踪号不能为空");
            }
        });

    }
    /**
     * 分销商-ERP释放库存 外部专用
     * @param distributorActivityCode 分销商活动编号
     * @param distributorActivityStockId 分销商活动库存id
     * @param quantity 释放的数量
     * @return boolean
     */
    public Boolean productLockInventoryReleaseOpenApi(String distributorActivityCode , Long distributorActivityStockId, Integer quantity){
        try {
            ErpProductLockInventoryReleaseRequest request = verificationReleaseRequestOpenApi(distributorActivityCode, distributorActivityStockId, quantity);
            return erpProductLockRequest(SysInfEnum.ERP_PRODUCT_LOCK_INVENTORY_RELEASE, JSONUtil.toJsonStr(List.of(request)), false);
        }catch (Exception e){
            log.error("ERP库存释放失败",e);
            return false;
        }
    }

    /**
     * 分销商释放库存请求参数封装
     * @param distributorActivityCode 供应商活动编号
     * @param distributorActivityStockId 分销商活动库存id
     * @param quantity 释放的数量
     */
    private ErpProductLockInventoryReleaseRequest verificationReleaseRequestOpenApi(String distributorActivityCode, Long distributorActivityStockId,Integer quantity) {
        if (StrUtil.isEmpty(distributorActivityCode)){
            throw new RuntimeException("[ERP库存释放],分销商活动编号不能为空");
        }
        if (ObjectUtil.isEmpty(distributorActivityStockId)){
            throw new RuntimeException("[ERP库存释放],分销商活动库存id不能为空");
        }
        if (ObjectUtil.isEmpty(quantity)){
            throw new RuntimeException("[ERP库存释放],释放数量不能为空");
        }
        DistributorProductActivity activity = distributorProductActivityService.getByActivityCode(distributorActivityCode);
        if (ObjectUtil.isEmpty(activity)){
            throw new RuntimeException(StrUtil.format("[ERP库存释放],分销商活动:{} 不存在",distributorActivityCode));
        }
        DistributorProductActivityStock stock = distributorProductActivityStockService.getById(distributorActivityStockId);
        if (ObjectUtil.isEmpty(stock)){
            throw new RuntimeException(StrUtil.format("[ERP库存释放],分销活动编号:{} 仓库编号:{} 不存在",distributorActivityCode,distributorActivityStockId));
        }
        //判断释放库存数量 是否大于 当前仓库锁定的库存
        if (quantity>stock.getQuantityTotal()){
            throw new RuntimeException(StrUtil.format("[ERP库存释放],分销活动编号:{} 仓库编号:{} 释放库存数不能大于锁货库存数",distributorActivityCode,distributorActivityStockId));
        }
        //构建请求体
        ErpProductLockInventoryReleaseRequest releaseRequest = new ErpProductLockInventoryReleaseRequest();
        releaseRequest.setOrgWarehouseCode(stock.getWarehouseCode());
        releaseRequest.setInventoryType(0);
        releaseRequest.setSrcLabel(90);
        String[] split = activity.getSupplierActivityCode().split("-");
        releaseRequest.setSourceNo(split[0]+"_"+stock.getId());
        releaseRequest.setQuantity(stock.getQuantityTotal());
        releaseRequest.setSku(activity.getProductSku());
        releaseRequest.setTrackCode(releaseRequest.getSourceNo()+"_"+releaseRequest.getSku());
        return releaseRequest;
    }

    /**
     * 供应商-ERP释放库存
     * @param supplierActivityCode 供应商活动编号
     * @param supplierActivityStockId 释放库存请求
     * @param quantity 释放数量
     * @return boolean
     */
    public Boolean erpProductLockInventoryRelease(String supplierActivityCode ,Long supplierActivityStockId, Integer quantity){
        try {
            ErpProductLockInventoryReleaseRequest request=   verificationReleaseRequest(supplierActivityCode,supplierActivityStockId,quantity);
            return erpProductLockRequest(SysInfEnum.ERP_PRODUCT_LOCK_INVENTORY_RELEASE, JSONUtil.toJsonStr(List.of(request)), false);
        }catch (Exception e){
            log.error("ERP库存释放失败",e);
            return false;
        }
    }

    /**
     * 供应商-释放库存
     * @param supplierActivityCode 供应商活动编号
     * @param activityStockId 释放库存请求
     * @param quantity 释放数量
     * @return boolean
     */
    private ErpProductLockInventoryReleaseRequest verificationReleaseRequest(String supplierActivityCode, Long activityStockId, Integer quantity) {

        if (StrUtil.isEmpty(supplierActivityCode)){
            throw new RuntimeException("[ERP库存释放],活动编号不能为空");
        }
        if (ObjectUtil.isEmpty(activityStockId)){
            throw new RuntimeException("[ERP库存释放],活动库存id不能为空");
        }
        if (ObjectUtil.isEmpty(quantity)){
            throw new RuntimeException("[ERP库存释放], 释放数量不能为空");
        }
        LambdaQueryWrapper<SupplierProductActivity> q = new LambdaQueryWrapper<>();
        q.eq(SupplierProductActivity::getSupplierActivityCode,supplierActivityCode);
        q.eq(SupplierProductActivity::getDelFlag,0);
        SupplierProductActivity one = supplierProductActivityService.getOne(q);
        if (ObjectUtil.isEmpty(one)){
            throw new RuntimeException(StrUtil.format("[ERP库存释放],活动编号:{} 不存在",supplierActivityCode));
        }
        SupplierProductActivityStock stock = supplierProductActivityStockService.getById(activityStockId);
        if (ObjectUtil.isEmpty(stock)){
            throw new RuntimeException(StrUtil.format("[ERP库存释放],活动编号:{} 仓库编号:{} 不存在",supplierActivityCode,activityStockId));
        }
        if (quantity>stock.getQuantityTotal()){
            throw new RuntimeException(StrUtil.format("[ERP库存释放],活动编号:{} 仓库编号:{} 释放库存数不能大于锁货库存数",supplierActivityCode,activityStockId));
        }
        //构建请求体
        ErpProductLockInventoryReleaseRequest releaseRequest = new ErpProductLockInventoryReleaseRequest();
        releaseRequest.setOrgWarehouseCode(stock.getWarehouseCode());
        releaseRequest.setInventoryType(0);
        releaseRequest.setSrcLabel(90);
        releaseRequest.setSourceNo(one.getSupplierTenantId()+"_"+stock.getId());
        releaseRequest.setQuantity(stock.getQuantityTotal());
        releaseRequest.setSku(one.getProductSku());
        releaseRequest.setTrackCode(releaseRequest.getSourceNo()+"_"+releaseRequest.getSku());
        return releaseRequest;
    }


    /**
     * erp库存接口封装
     * @param infNote 接口枚举类
     * @param jsonBody 请求体
     * @param isReserved 是否是锁定库存
     * @return boolean
     */
    public Boolean erpProductLockRequest(String infNote,String jsonBody,Boolean isReserved){
        if (StrUtil.isEmpty(jsonBody)){
            throw new RuntimeException("请求体不能为空");
        }
        if (ObjectUtil.isNull(isReserved)){
            throw new RuntimeException("是否是锁定库存不能为空");
        }
        // 根据isReserved参数动态设置日志前缀
        String logPrefix = isReserved ? "[ERP库存锁定]" : "[ERP库存释放]";

        try {
            SysInfVo sysInfVo = sysInfService.queryByInfNote(infNote);
            if (ObjectUtil.isNull(sysInfVo)){
                throw new RuntimeException(StrUtil.format("{}报错,错误原因：{} 未获取接口相关配置", logPrefix, "ERP_PRODUCT_LOCK_INVENTORY_RESERVED"));
            }
            String url=null;
            if (sysInfVo.getIsTest()==1){
                url=sysInfVo.getInfTestUrl();
            }
            if (sysInfVo.getIsTest()==2){
                url=sysInfVo.getInfUrl();
            }
            String infParameters = sysInfVo.getInfParameters();
            JSONObject jsonObject = JSONUtil.parseObj(infParameters);
            String headerApiKey = jsonObject.getStr("token");
            //  log.error(StrUtil.format("[获取ERP尾程派送费]请求地址：{},请求Key:{}，请求参数:{}",url,headerApiKey));
            log.error(StrUtil.format("{}请求参数：{}", logPrefix, jsonBody));
            String result2 = HttpRequest.post(url)
                                        .header("Authorization",headerApiKey)
                                        .body(jsonBody)
                                        //超时，毫秒
                                        .timeout(10000)
                                        .execute().body();
            log.error(StrUtil.format("{}返回参数：{}", logPrefix, ObjectUtil.isNull(result2)? "": result2.replaceAll("\\r?\\n", "")));
            JSONObject object = JSONUtil.parseObj(result2);
            Integer code = object.getInt("code", -1);
            if (code != 200){
                throw new RuntimeException(StrUtil.format("{}失败，ERP返回：{},", logPrefix, ObjectUtil.isNull(result2)? "": result2.replaceAll("\\r?\\n", "")));
            }else {
                return true;
            }
        }catch (Exception e){
            log.error(StrUtil.format("{}失败", logPrefix), e);
            return false;
        }
    }



    /**
     * 查询所有供应商的锁定活动，按productSKU分组
     * @return Map<productSKU, 供应商活动列表>
     */
    public Map<String,List<SupplierProductActivity>> getSupplierActives(){
        // 1. 查询所有分销商活动
        List<SupplierProductActivity> activityList = supplierProductActivityService.getBaseMapper().selectList();
        return activityList.stream().collect(Collectors.groupingBy(SupplierProductActivity::getProductSku));
    }
    /**
     * 查询所有分销商的锁定活动，按供应商活动id分组
     * @return Map<productSKU, 供应商活动列表>
     */
    public Map<String,List<DistributorProductActivity>> getDistributorActives(){
        // 1. 查询所有分销商活动
        List<DistributorProductActivity> activityList = distributorProductActivityService.getBaseMapper()
                                                                                         .selectList();
        return activityList.stream().collect(Collectors.groupingBy(DistributorProductActivity::getSupplierActivityCode));
    }


    /**
     * 获取按SKU+仓库系统编码+锁库存方式分组的库存总和
     * 如果当前商品有两个发货方式（自提/代发），则相当于有两条记录（自提一个/代发一个）
     * @return Map<String, Long> key是 sku+仓库系统编码+锁库存方式，value是锁库存总和
     */
    public Map<String, Long> getStockSummaryBySkuWarehouseLogistics() {
        // 1. 获取所有供应商活动库存数据
        List<ProductActivityStockPullDTO> stockList = supplierProductActivityService.getBaseMapper().getProductActivityStockPullDTO();

        // 2. 按照 sku+仓库系统编码+锁库存方式 分组并求和
        return stockList.stream()
                        .collect(Collectors.groupingBy(
                            stock -> stock.getProductSku() + "+" + stock.getWarehouseSystemCode() + "+" + stock.getSupportedLogistics(),
                            Collectors.summingLong(ProductActivityStockPullDTO::getQuantityTotal)
                        ));
    }

    /**
     *
     * @return Map<String, List<SupplierProductActivity>> key是 sku+仓库系统编码，value活动去重
     */
    public Map<String, List<ProductActivityStockPullDTO>> getSupplierActiveBySkuAndWarehouse() {
        // 1. 获取所有供应商活动库存数据
        List<ProductActivityStockPullDTO> stockList = supplierProductActivityService.getBaseMapper().getProductActivityStockPullDTO();
        // 2. 按照 sku+仓库系统编码 分组
        return stockList.stream()
                        .collect(Collectors.groupingBy(stock -> stock.getProductSku() + "+" + stock.getWarehouseSystemCode()));
    }



    /**
     * 根据SPU获取可用的分销商活动
     * @param productCode 商品编码
     * @param site 站点
     * @return Map<String,Map<String,List<DistributorProductActivityDetails>>>
     *         第一个key是productSkuCode，第二个key是supportedLogistics（自提/代发）
     */
    public Map<String,Map<String,List<DistributorProductActivityDetails>>> getDistributorAvailableActivesMapBySpu(String productCode,String site) {
        if (StrUtil.isEmpty(site) || StrUtil.isEmpty(productCode)){
            return new HashMap<>();
        }
        List<DistributorProductActivityDetails> activesBySku =TenantHelper.ignore(()->distributorProductActivityService.getBaseMapper().getDistributorAvailableActivesBySpu( productCode, site));
        if (CollUtil.isEmpty(activesBySku)) {
            return new HashMap<>();
        }
        // 按商品SKU编码和发货方式进行两级分组
        return activesBySku.stream()
                .collect(Collectors.groupingBy(
                    DistributorProductActivityDetails::getProductSkuCode,
                    Collectors.groupingBy(
                        DistributorProductActivityDetails::getSupportedLogistics,
                        Collectors.collectingAndThen(
                            Collectors.toList(),
                            list -> list.stream()
                                    .sorted((a, b) -> Long.compare(b.getId(), a.getId())) // 按id降序排序
                                    .collect(Collectors.toList())
                        )
                    )
                ));
    }

    /**
     * 根据SPU获取可用的供应商活动
     * @param productCode 商品编码
     * @param site 站点
     * @return Map<String,Map<String,List<SupplierProductActivityDetails>>>
     *         第一个key是productSkuCode，第二个key是supportedLogistics（自提/代发）
     */
    public Map<String,Map<String,List<SupplierProductActivityDetails>>> getSupplierAvailableActivesMapBySpu(String productCode,String site) {
        if (StrUtil.isEmpty(site) || StrUtil.isEmpty(productCode)){
            return new HashMap<>();
        }
        List<SupplierProductActivityDetails> activesBySku = TenantHelper.ignore(()->supplierProductActivityService.getBaseMapper().getSupplierAvailableActivesBySpu(productCode, site));
        if (CollUtil.isEmpty(activesBySku)) {
            return new HashMap<>();
        }

        // 第一步：按productSkuCode分组并按ID倒序排列
        Map<String, List<SupplierProductActivityDetails>> groupedBySkuCode = activesBySku.stream()
                .filter(Objects::nonNull)
                .filter(activity -> activity.getSupplierProductActivityStocks() != null && !activity.getSupplierProductActivityStocks().isEmpty())
                .collect(Collectors.groupingBy(
                    SupplierProductActivityDetails::getProductSkuCode,
                    Collectors.collectingAndThen(
                        Collectors.toList(),
                        list -> list.stream()
                                .sorted((a, b) -> {
                                    if (a.getId() == null && b.getId() == null) return 0;
                                    if (a.getId() == null) return 1;
                                    if (b.getId() == null) return -1;
                                    return b.getId().compareTo(a.getId()); // 按id降序排序
                                })
                                .collect(Collectors.toList())
                    )
                ));

        // 第二步：对每个商品的活动，按库存信息的发货方式进行分组
        Map<String, Map<String, List<SupplierProductActivityDetails>>> result = new HashMap<>();

        groupedBySkuCode.forEach((skuCode, activities) -> {
            Map<String, List<SupplierProductActivityDetails>> logisticsMap = new HashMap<>();

            // 遍历每个活动，根据其库存信息的发货方式进行分组
            activities.forEach(activity -> {
                if (activity.getSupplierProductActivityStocks() != null) {
                    // 按发货方式对库存进行分组
                    Map<String, List<SupplierProductActivityStock>> stocksByLogistics = activity.getSupplierProductActivityStocks().stream()
                            .filter(Objects::nonNull)
                            .filter(stock -> stock.getSupportedLogistics() != null)
                            .collect(Collectors.groupingBy(SupplierProductActivityStock::getSupportedLogistics));

                    // 为每种发货方式创建活动副本（包含对应的库存信息）
                    stocksByLogistics.forEach((logistics, stocks) -> {
                        // 创建活动副本，只包含当前发货方式的库存
                        SupplierProductActivityDetails activityCopy = BeanUtil.copyProperties(activity, SupplierProductActivityDetails.class);
                        activityCopy.setSupplierProductActivityStocks(stocks);

                        logisticsMap.computeIfAbsent(logistics, k -> new ArrayList<>()).add(activityCopy);
                    });
                }
            });

            result.put(skuCode, logisticsMap);
        });

        return result;
    }

    /**
     * 获取指定SKU的自提最低价格和代发最低价格
     * @param productSkuCode SKU编码
     * @param supplierActivesMap
     * @return Map<String, BigDecimal> key为价格类型（pickup/dropShipping），value为最低价格
     */
    public Map<String, BigDecimal> getSkuLowestPrices(Map<String,Map<String,List<SupplierProductActivityDetails>>> supplierActivesMap,String productSkuCode) {
        Map<String, BigDecimal> result = new HashMap<>();

        if (CollUtil.isEmpty(supplierActivesMap)) {
            return result;
        }

        // 只处理指定SKU的活动
        Map<String, List<SupplierProductActivityDetails>> map = supplierActivesMap.get(productSkuCode);
        if (CollUtil.isEmpty(map)) {
            return result;
        }

        BigDecimal lowestPickupPrice = null;
        BigDecimal lowestDropShippingPrice = null;

        // 处理自提价格
        List<SupplierProductActivityDetails> pickupActivities = map.get("PickUpOnly");
        if (CollUtil.isNotEmpty(pickupActivities)) {
            lowestPickupPrice = pickupActivities.stream()
                                                .map(SupplierProductActivityDetails::getSupplierActivityPickUpPrice)
                                                .filter(Objects::nonNull)
                                                .min(BigDecimal::compareTo)
                                                .orElse(null);
        }

        // 处理代发价格
        List<SupplierProductActivityDetails> dropShippingActivities = map.get("DropShippingOnly");
        if (CollUtil.isNotEmpty(dropShippingActivities)) {
            lowestDropShippingPrice = dropShippingActivities.stream()
                                                            .map(SupplierProductActivityDetails::getSupplierActivityDropShippingPrice)
                                                            .filter(Objects::nonNull)
                                                            .min(BigDecimal::compareTo)
                                                            .orElse(null);
        }

        // 设置结果
        if (lowestPickupPrice != null) {
            result.put("PickUpOnly", lowestPickupPrice);
        }
        if (lowestDropShippingPrice != null) {
            result.put("DropShippingOnly", lowestDropShippingPrice);
        }

        return result;
    }


    /**
     * 商品活动加锁调整库存
     *
     * @param dto
     * @return
     * @throws StockException
     */
    @InMethodLog("商品活动加锁调整库存")
    @Transactional(rollbackFor = Exception.class)
    public String lockAdjustStock(AdjustStockDTO dto) throws StockException {
        String destCountry = dto.getDestCountry();
        String productSkuCode = dto.getProductSkuCode();
        String activityCode = dto.getActivityCode();
        Integer adjustQuantity = dto.getAdjustQuantity();
        String warehouseSystemCode = dto.getSpecifyWarehouse();
        Boolean logisticsAccount = dto.getLogisticsAccount();
        LogisticsTypeEnum logisticsType = dto.getLogisticsType();

        RedissonClient client = RedisUtils.getClient();
        String key = RedisConstants.ZSMALL_PRODUCT_ACTIVITY_ITEM_LOCK + activityCode;
        RLock lock = client.getLock(key);

        String finalWarehouseSystemCode = dto.getSpecifyWarehouse();
        try {
            lock.lock(10L, TimeUnit.SECONDS);
            // 持锁后，取消该分销商的活动
            log.info("活动订单库存调整{} 加锁成功", activityCode);
            // 分销商活动
            DistributorProductActivity distributorProductActivity = TenantHelper.ignore(() -> distributorProductActivityService.getOne(new LambdaQueryWrapper<DistributorProductActivity>()
                .eq(DistributorProductActivity::getDistributorActivityCode, activityCode)
                .eq(DistributorProductActivity::getActivityState, ProductActivityStateEnum.InProgress.getValue())
                .eq(DistributorProductActivity::getSupportedLogistics, logisticsType.getValue())));
            if(null == distributorProductActivity){
                log.info("分销商活动{} 不存在", activityCode);
                throw new StockException(ZSMallStatusCodeEnum.ACTIVITY_NOT_EXIST.args(activityCode));
            }
            if(null != distributorProductActivity.getActivityState() && !ProductActivityStateEnum.InProgress.getValue().equals(distributorProductActivity.getActivityState())){
                log.info("分销商活动{} 状态不是进行中", activityCode);
                throw new StockException(ZSMallStatusCodeEnum.ACTIVITY_NOT_AVAILABLE.args(activityCode));
            }
            // 分销商活动库存
            DistributorProductActivityStock distributorProductActivityStock = TenantHelper.ignore(() -> distributorProductActivityStockService.getOne(
                new LambdaQueryWrapper<DistributorProductActivityStock>().eq(DistributorProductActivityStock::getDistributorActivityId, distributorProductActivity.getId())
                                                                         .eq(DistributorProductActivityStock::getWarehouseSystemCode, warehouseSystemCode)
                                                                         .eq(DistributorProductActivityStock::getSupportedLogistics, logisticsType.getValue())
            ));
            if(null == distributorProductActivityStock){
                log.info("分销商活动{} 库存不存在", activityCode);
                throw new StockException(ZSMallStatusCodeEnum.ACTIVITY_WAREHOUSE_NOT_EXIST.args(activityCode));
            }
            // adjustQuantity 的绝对值
            Integer adjustQuantityAbs = Math.abs(adjustQuantity);
            if(null != distributorProductActivityStock.getQuantitySurplus() && distributorProductActivityStock.getQuantitySurplus() < adjustQuantityAbs){
                log.info("分销商活动{} 库存不足", activityCode);
                throw new StockException(ZSMallStatusCodeEnum.ACTIVITY_QUANTITY_NOT_ENOUGH.args(activityCode));
            }
            Long supplierActivityId = distributorProductActivity.getSupplierActivityId();
            SupplierProductActivity supplierProductActivity = TenantHelper.ignore(() ->
                supplierProductActivityService.getOne(new LambdaQueryWrapper<SupplierProductActivity>().eq(SupplierProductActivity::getId, supplierActivityId))
            );
            if(null == supplierProductActivity){
                log.info("供应商活动{} 不存在", activityCode);
                throw new StockException(ZSMallStatusCodeEnum.ACTIVITY_NOT_EXIST.args(activityCode));
            }
            if(null != supplierProductActivity.getActivityState() && !ProductActivityStateEnum.InProgress.getValue().equals(supplierProductActivity.getActivityState())){
                log.info("供应商活动{} 状态不是进行中", activityCode);
                throw new StockException(ZSMallStatusCodeEnum.ACTIVITY_NOT_AVAILABLE.args(activityCode));
            }
            SupplierProductActivityStock supplierProductActivityStock = TenantHelper.ignore(() -> supplierProductActivityStockService.getOne(new LambdaQueryWrapper<SupplierProductActivityStock>()
                .eq(SupplierProductActivityStock::getSupplierActivityId, supplierActivityId)
                .eq(SupplierProductActivityStock::getWarehouseSystemCode, warehouseSystemCode)
                .eq(SupplierProductActivityStock::getSupportedLogistics, logisticsType.getValue())
            ));
            if (null == supplierProductActivityStock){
                log.info("供应商活动{} 库存不存在", activityCode);
                throw new StockException(ZSMallStatusCodeEnum.ACTIVITY_WAREHOUSE_NOT_EXIST.args(activityCode));
            }
            if (null != supplierProductActivityStock.getQuantitySurplus() && supplierProductActivityStock.getQuantitySurplus() < adjustQuantityAbs){
                log.info("供应商活动{} 库存不足", activityCode);
            }

            // 调整库存数值为负数时（说明是扣减）
            if (adjustQuantity < 0) {
                // 仓库可配送国家判断
                List<String> countryCodeList = TenantHelper.ignore(() -> iWarehouseService.listCountryByWarehouseSystemCode(warehouseSystemCode));
                if(CollUtil.isNotEmpty(countryCodeList)){
                    if(!countryCodeList.contains(destCountry)){
//                    order.setOrderState(OrderStateType.Failed);
//                    JSONObject errorMsg = LocaleMessage.byStatusCode(OrderStatusCodeEnum.ORDER_CANNOT_MATCH_WAREHOUSE)
//                                                       .toJSON();
//                    order.setPayErrorMessage(errorMsg);
                        throw new StockException(OrderStatusCodeEnum.ORDER_CANNOT_MATCH_WAREHOUSE);
                    }
                }else {
                    throw new StockException(OrderStatusCodeEnum.ORDER_CANNOT_MATCH_WAREHOUSE);
                }
                // 分销商活动库存数据更新
                Integer distributorProductActivityStockQuantitySurplus = distributorProductActivityStock.getQuantitySurplus();
                Integer distributorProductActivityStockQuantitySold = distributorProductActivityStock.getQuantitySold();
                distributorProductActivityStock.setQuantitySurplus(distributorProductActivityStockQuantitySurplus + adjustQuantity);
                distributorProductActivityStock.setQuantitySold(distributorProductActivityStockQuantitySold - adjustQuantity);
                distributorProductActivityStockService.updateById(distributorProductActivityStock);
                // 分销商活动订单数量更新
                Integer distributorProductActivityOrderedTotal = distributorProductActivity.getOrderedTotal();
                distributorProductActivity.setOrderedTotal(distributorProductActivityOrderedTotal - adjustQuantity);
                // 供应商活动库存数据更新
                Integer supplierProductActivityStockQuantitySold = supplierProductActivityStock.getQuantitySold();
                Integer supplierProductActivityStockQuantitySurplus = supplierProductActivityStock.getQuantitySurplus();
                supplierProductActivityStock.setQuantitySold(supplierProductActivityStockQuantitySold - adjustQuantity);
                supplierProductActivityStock.setQuantitySurplus(supplierProductActivityStockQuantitySurplus + adjustQuantity);
                supplierProductActivityStockService.updateById(supplierProductActivityStock);
                // 供应商活动订单数量更新
                Integer supplierProductActivityOrderedTotal = supplierProductActivity.getOrderedTotal();
                supplierProductActivity.setOrderedTotal(supplierProductActivityOrderedTotal - adjustQuantity);
                supplierProductActivityService.updateById(supplierProductActivity);
                // 自提订单处理
                if (LogisticsTypeEnum.PickUp.equals(logisticsType)) {
                    // 分销商活动自提订单已使用数量
                    Integer distributorProductActivityPickupLockedUsed = distributorProductActivity.getPickupLockedUsed();
                    distributorProductActivity.setPickupLockedUsed(distributorProductActivityPickupLockedUsed - adjustQuantity);
                }
                // 代发订单处理
                if (LogisticsTypeEnum.DropShipping.equals(logisticsType)) {
                    // 分销商活动代发订单已使用数量
                    Integer distributorProductActivityDropShippingLockedUsed = distributorProductActivity.getDropShippingLockedUsed();
                    distributorProductActivity.setDropShippingLockedUsed(distributorProductActivityDropShippingLockedUsed - adjustQuantity);
                }
                distributorProductActivityService.updateById(distributorProductActivity);
            }else {
                // else说明adjustQuantity>=0，是归还库存
                // 分销商活动库存数据更新
                Integer distributorProductActivityStockQuantitySurplus = distributorProductActivityStock.getQuantitySurplus();
                Integer distributorProductActivityStockQuantitySold = distributorProductActivityStock.getQuantitySold();
                distributorProductActivityStock.setQuantitySurplus(distributorProductActivityStockQuantitySurplus - adjustQuantity);
                distributorProductActivityStock.setQuantitySold(distributorProductActivityStockQuantitySold + adjustQuantity);
                distributorProductActivityStockService.updateById(distributorProductActivityStock);
                // 分销商活动订单数量更新
                Integer distributorProductActivityOrderedTotal = distributorProductActivity.getOrderedTotal();
                distributorProductActivity.setOrderedTotal(distributorProductActivityOrderedTotal + adjustQuantity);
                // 供应商活动库存数据更新
                Integer supplierProductActivityStockQuantitySold = supplierProductActivityStock.getQuantitySold();
                Integer supplierProductActivityStockQuantitySurplus = supplierProductActivityStock.getQuantitySurplus();
                supplierProductActivityStock.setQuantitySold(supplierProductActivityStockQuantitySold + adjustQuantity);
                supplierProductActivityStock.setQuantitySurplus(supplierProductActivityStockQuantitySurplus - adjustQuantity);
                supplierProductActivityStockService.updateById(supplierProductActivityStock);
                // 供应商活动订单数量更新
                Integer supplierProductActivityOrderedTotal = supplierProductActivity.getOrderedTotal();
                supplierProductActivity.setOrderedTotal(supplierProductActivityOrderedTotal + adjustQuantity);
                supplierProductActivityService.updateById(supplierProductActivity);
                // 自提订单处理
                if (LogisticsTypeEnum.PickUp.equals(logisticsType)) {
                    // 分销商活动自提订单已使用数量
                    Integer distributorProductActivityPickupLockedUsed = distributorProductActivity.getPickupLockedUsed();
                    distributorProductActivity.setPickupLockedUsed(distributorProductActivityPickupLockedUsed + adjustQuantity);
                }
                // 代发订单处理
                if (LogisticsTypeEnum.DropShipping.equals(logisticsType)) {
                    // 分销商活动代发订单已使用数量
                    Integer distributorProductActivityDropShippingLockedUsed = distributorProductActivity.getDropShippingLockedUsed();
                    distributorProductActivity.setDropShippingLockedUsed(distributorProductActivityDropShippingLockedUsed + adjustQuantity);
                }
                distributorProductActivityService.updateById(distributorProductActivity);

            }


//            ProductActivityItem productActivityItem = iProductActivityItemService.queryOneByEntity(
//                ProductActivityItem.builder().activityCode(activityCode).build()
//            );

//            Long activityItemId = productActivityItem.getId();
//            ProductActivityTypeEnum activityType = productActivityItem.getActivityType();
//            Integer quantitySurplus = productActivityItem.getQuantitySurplus();
//
//            ProductActivityStockLockItem activityStockLockItem = iProductActivityStockLockItemService.queryByActivityItemId(activityItemId);
//            ProductActivityBuyoutItem activityBuyoutItem = iProductActivityBuyoutItemService.queryByActivityItemId(activityItemId);

            // 调整库存数值为负数时（说明是扣减）
//            if (adjustQuantity < 0) {
//                if (!ProductActivityItemStateEnum.InProgress.equals(productActivityItem.getActivityState())) {
//                    throw new StockException(ZSMallStatusCodeEnum.ACTIVITY_NOT_AVAILABLE.args(activityCode));
//                }
//
//                // 总剩余库存对比
//                if (quantitySurplus < Math.abs(adjustQuantity)) {
//                    throw new StockException(ZSMallStatusCodeEnum.PRODUCT_SKU_OUT_OF_STOCK.args(productSkuCode));
//                }
//
//                // 锁货活动还需要判断是否过期
//                if (ProductActivityTypeEnum.StockLock.equals(activityType)) {
//                    Date expiryDateTime = activityStockLockItem.getExpiryDateTime();
//                    //活动过期
//                    if (DateUtil.compare(expiryDateTime, new Date()) <= 0) {
//                        throw new StockException(ZSMallStatusCodeEnum.ACTIVITY_NOT_AVAILABLE.args(activityCode));
//                    }
//                }
//            } else {  // else说明adjustQuantity>=0，是归还库存
//                if (!ProductActivityItemStateEnum.InProgress.equals(productActivityItem.getActivityState())) {
//
//                    // 若子活动不为进行中，则需要将定金退到供货商的账单中，然后直接结束本方法，不再往下归还库存
//                    if (productActivityItem != null) {
//                        ProductActivity productActivity = iProductActivityService.queryOneByEntity(ProductActivity.builder()
//                            .activityCode(productActivityItem.getActivityCodeParent()).build());
//                        // 开始计算剩余订金及剩余库存
//                        ProductActivityPriceItem activityPriceItem =
//                            iProductActivityPriceItemService.queryByActivityItemId(activityItemId);
//                        BigDecimal activityDepositUnitPrice = activityPriceItem.getActivityDepositUnitPrice();
//                        BigDecimal platformDepositUnitPrice = activityPriceItem.getPlatformDepositUnitPrice();
//
//                        //记录扣除的订金到结算表
//                        ProductActivityCheckout checkout = new ProductActivityCheckout();
//                        checkout.setSupplierTenantId(productActivity.getTenantId());
//                        checkout.setDistributorTenantId(productActivityItem.getTenantId());
//                        checkout.setProductActivityId(productActivity.getId());
//                        checkout.setProductActivityItemId(activityItemId);
//                        checkout.setCheckoutType(ActivityCheckoutTypeEnum.PenaltyFee);
//                        checkout.setCheckoutUnitPrice(activityDepositUnitPrice);
//                        checkout.setCheckoutAmount(NumberUtil.mul(activityDepositUnitPrice, adjustQuantity));
//                        checkout.setPlatformCheckoutUnitPrice(platformDepositUnitPrice);
//                        checkout.setPlatformCheckoutAmount(NumberUtil.mul(platformDepositUnitPrice, adjustQuantity));
//                        checkout.setCheckoutQuantity(adjustQuantity);
//                        checkout.setDistributorPay(CheckoutPayEnum.Paid);
//                        iProductActivityCheckoutService.save(checkout);
//                        iProductActivityPriceItemService.updateById(activityPriceItem);
//                        billSupport.generateBillDTOByProductActivityCheckout(checkout, null);
//                    }
//
//                    return finalWarehouseSystemCode;
//                }

//                if (StrUtil.isBlank(warehouseSystemCode)) {
//                    throw new StockException(ZSMallStatusCodeEnum.BACK_INVENTORY_NO_SPECIAL_WAREHOUSE);
//                }
//            }

            // 调整指定仓库的库存
//            ProductActivityStockItem finalStockItem;
//            if (StrUtil.isNotBlank(warehouseSystemCode)) {
//                // 匹配仓库进行库存调整
////                finalStockItem = iProductActivityStockItemService.queryByActivityItemIdAndWarehouse(activityItemId, specifyWarehouse);
//            } else if (adjustQuantity < 0) {


            // 不指定仓库时，调整库存（只有扣除才能不指定仓库）
            // 1.查询库存充足的仓库的zipCode集合，去重
//                List<ProductActivityStockItem> stockItemList = iProductActivityStockItemService.queryAdequateStockByParams(destCountry, activityItemId, adjustQuantity, logisticsType, logisticsAccount);

            // 自提的因为是全世界范围内选择库存，所以需要判断有没有当前国家的库存，若有则优先取当前国家的库存，没有则取其他国家的随机
//                if (LogisticsTypeEnum.PickUp.equals(logisticsType)) {
////                    List<ProductActivityStockItem> currentCountry = stockItemList.stream().filter(stock -> stock.getCountry().equals(destCountry)).collect(Collectors.toList());
////                    if (CollUtil.isNotEmpty(currentCountry)) {
////                        stockItemList = currentCountry;
////                    }
//                }

//                if (CollUtil.size(stockItemList) == 1) {
//                    finalStockItem = stockItemList.get(0);
//                } else if (CollUtil.size(stockItemList) > 1) {
//                    finalStockItem = stockItemList.get(RandomUtil.randomInt(0, CollUtil.size(stockItemList)));
//                } else {
//                    finalStockItem = null;
//                }
//            } else {
////                finalStockItem = null;
//            }

//            if (finalStockItem != null) {
//            if(1==1){
//                Integer quantityTotal = finalStockItem.getQuantityTotal();
//                Integer quantitySold = finalStockItem.getQuantitySold();
//                Integer newQuantitySold = NumberUtil.sub(quantitySold, adjustQuantity).intValue();
//                Integer newQuantitySurplus = NumberUtil.sub(quantityTotal, newQuantitySold).intValue();
//
//                if (newQuantitySurplus >= 0) {
//                    finalStockItem.setQuantitySold(newQuantitySold);
//                    finalStockItem.setQuantitySurplus(newQuantitySurplus);
//
//                    finalWarehouseSystemCode = finalStockItem.getWarehouseSystemCode();
//                } else if (adjustQuantity < 0) {  // 只有扣除库存时需要报错库存不足，归还库存时不需要报错
//                    throw new StockException(ZSMallStatusCodeEnum.PRODUCT_SKU_OUT_OF_STOCK.args(productSkuCode));
//                }
//
//                // 重新计算子活动的数量
//                Integer quantityTotal_pai = productActivityItem.getQuantityTotal();
//                Integer quantitySold_pai = productActivityItem.getQuantitySold();
//
//                Integer newQuantitySold_pai = NumberUtil.sub(quantitySold_pai, adjustQuantity).intValue();
//                Integer newQuantitySurplus_pai = NumberUtil.sub(quantityTotal_pai, newQuantitySold_pai).intValue();
//
//                productActivityItem.setQuantitySold(newQuantitySold_pai);
//                productActivityItem.setQuantitySurplus(newQuantitySurplus_pai);
//
//                // 开始计算剩余订金及剩余库存
//                ProductActivityPriceItem activityPriceItem =
//                    iProductActivityPriceItemService.queryByActivityItemId(activityItemId);
//
//                // 调整活动定金剩余（供货商）
//                // 获取活动定金单价（供货商）
//                BigDecimal activityDepositUnitPrice = activityPriceItem.getActivityDepositUnitPrice();
//                // 调整的定金总金额
//                BigDecimal adjustActivityDepositAmount = NumberUtil.mul(activityDepositUnitPrice, adjustQuantity);
//                BigDecimal activityDepositSurplusPrice = activityPriceItem.getActivityDepositSurplusPrice();
//                activityDepositSurplusPrice = NumberUtil.add(activityDepositSurplusPrice, adjustActivityDepositAmount);
//                activityPriceItem.setActivityDepositSurplusPrice(activityDepositSurplusPrice);
//
//                // 调整平台定金剩余（平台）
//                // 获取平台定金单价（平台）
//                BigDecimal platformDepositUnitPrice = activityPriceItem.getPlatformDepositUnitPrice();
//                // 调整的定金总金额
//                BigDecimal adjustPlatformDepositAmount = NumberUtil.mul(platformDepositUnitPrice, adjustQuantity);
//                BigDecimal platformDepositSurplusPrice = activityPriceItem.getPlatformDepositSurplusPrice();
//                platformDepositSurplusPrice = NumberUtil.add(platformDepositSurplusPrice, adjustPlatformDepositAmount);
//                activityPriceItem.setPlatformDepositSurplusPrice(platformDepositSurplusPrice);
//
//                TenantHelper.ignore(() -> {
//                    iProductActivityItemService.updateById(productActivityItem);
//                    iProductActivityStockItemService.updateById(finalStockItem);
//                    iProductActivityPriceItemService.updateById(activityPriceItem);
//                });
//
//                if (activityStockLockItem != null) {
//                    activityStockLockItem.setStockLockSold(newQuantitySold_pai);
//                    activityStockLockItem.setStockLockSurplus(newQuantitySurplus_pai);
//
//                    TenantHelper.ignore(() -> {
//                        iProductActivityStockLockItemService.updateById(activityStockLockItem);
//                    });
//                }
//
//                if (activityBuyoutItem != null) {
//                    activityBuyoutItem.setBuyoutSold(newQuantitySold_pai);
//                    activityBuyoutItem.setBuyoutSurplus(newQuantitySurplus_pai);
//
//                    TenantHelper.ignore(() -> {
//                        iProductActivityBuyoutItemService.updateById(activityBuyoutItem);
//                    });
//                }

            // 调整成功，创建库存同步任务
//                ZSMallProductEventUtils.createTaskStockSync(productSkuCode);
//            } else {
//                throw new StockException(ZSMallStatusCodeEnum.NO_AVAILABLE_STOCK_FOUND.args(productSkuCode));
//            }
        } catch (RStatusCodeException e) {
            log.error("活动商品[{}]库存调整出现状态码错误 = {}", productSkuCode, e.getMessage(), e);
            RStatusCodeBase statusCode = e.getStatusCode();
            throw new StockException(statusCode);
        } catch (StockException e) {
            throw e;
        } catch (Exception e) {
            log.error("活动商品[{}]库存调整出现未知错误 = {}", productSkuCode, e.getMessage(), e);
            throw new StockException(ZSMallStatusCodeEnum.PRODUCT_SKU_ADJUST_STOCK_ERROR.args(productSkuCode));
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
            log.info("子活动{} 解锁成功", activityCode);
        }
        return finalWarehouseSystemCode;
    }

    @InMethodLog("活动定金钱包支付")
    public void activityDepositPay(ActivityDepositPayBo activityDepositPayBo) throws Exception {
        TransactionRecord transactionRecord = new TransactionRecord(mallSystemCodeGenerator.codeGenerate(BusinessCodeEnum.TransactionNo));
        transactionRecord.setTransactionType(TransactionTypeEnum.Expenditure);
        transactionRecord.setTransactionSubType(TransactionSubTypeEnum.StockLockDeposit);
        transactionRecord.setCurrency(activityDepositPayBo.getCurrency());
        transactionRecord.setCurrencySymbol(activityDepositPayBo.getCurrencySymbol());
        // 分销平台内实际交易金额
        transactionRecord.setTransactionAmount(activityDepositPayBo.getAmount());
        LambdaQueryWrapper<TenantWallet> twLqw = new LambdaQueryWrapper<>();
        String tenantId = activityDepositPayBo.getTenantId();
        twLqw.eq(TenantWallet::getTenantId, tenantId);
        if(StringUtils.isNotBlank(activityDepositPayBo.getCurrency())){
            twLqw.eq(TenantWallet::getCurrency, activityDepositPayBo.getCurrency());
        }
        TenantWallet tenantWallet = tenantWalletMapper.selectOne(twLqw);
        BigDecimal balance = BigDecimal.ZERO;
        if (ObjectUtil.isNotNull(tenantWallet)) {
            balance = tenantWallet.getWalletBalance();
        }
        transactionRecord.setBeforeBalance(balance);
        transactionRecord.setAfterBalance(balance.subtract(activityDepositPayBo.getAmount()));
        transactionRecord.setTransactionState(TransactionStateEnum.Processing);
        transactionRecord.setTransactionTime(new Date());
        iTransactionRecordService.save(transactionRecord);
        tenantWalletService.walletChanges(transactionRecord);
    }

}
